'use client';

import { useState, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
// Dialog 相关导入暂时移除，因为未使用
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/Popover';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/Tooltip';
// import { useTips } from '@/lib/ui'; // 暂时注释掉，因为找不到模块
import Image from 'next/image';
import { BackgroundBlurPicker } from './components/BackgroundBlurPicker';
import { BackgroundColorPicker } from './components/BackgroundColorPicker';
import { BackgroundImagePicker } from './components/BackgroundImagePicker';
import { CanvasImageEditor } from './components/CanvasImageEditor';
import { EraserTool } from './components/EraserTool';
import { BackgroundRemovalErrorOverlay } from './components/BackgroundRemovalErrorOverlay';
import StarryNightLoading from '../starry-night-loading';
import MoveIcon from '../icons/Move';
import { ImageUploadArea } from './components/ImageUploadArea';
import { ImageHistoryBar } from './components/ImageHistoryBar';
import { CreditDisplay } from './components/CreditDisplay';
import { useImageStore } from '@/store/imageStore';

// 导入新的模块
import { useImageUpload } from '../../hooks/background-remover/useImageUpload';
import { useImageEditor } from '../../hooks/background-remover/useImageEditor';
import { useImageStorage } from '../../hooks/background-remover/useImageStorage';
import { formatImageDimensions } from '@/lib/imageUtils/imageResize';

/**
 * 重构后的背景移除组件，功能已拆分到多个模块中
 */
export function BackgroundRemover() {
  // 使用自定义背景图片存储Hook
  const {
    uploadedBackgroundImages,
    addBackgroundImage,
    deleteBackgroundImage,
  } = useImageStorage();

  // 上传相关 hook
  const {
    isLoadingApi,
    isDragActive,
    getRootProps,
    getInputProps,
    open,
    handleLoadFromUrl,
    handleLoadSampleImage,
    processingImageIds,
    processImageBackgroundRemoval,
  } = useImageUpload();

  // 删除后图片选择处理，包含自动去背逻辑
  const handleImageSelectAfterDelete = useCallback(
    async (imageId: string): Promise<void> => {
      const image = useImageStore.getState().images.get(imageId);
      if (!image) return;

      // 使用通用的自动背景去除函数
      await processImageBackgroundRemoval(image);
    },
    [processImageBackgroundRemoval]
  );

  // 监听背景图片清理事件
  useEffect(() => {
    const handleCleanupBackgroundImage = async (event: CustomEvent) => {
      const { url, id } = event.detail;

      try {
        // 跳过预设背景图片的清理
        if (id?.startsWith('preset-')) {
          console.log('跳过预设背景图片清理:', id);
          return;
        }

        console.log('收到背景图片清理事件:', { url, id });

        // 再次确认没有图片在使用这个背景图片
        const allImages = Array.from(useImageStore.getState().images.values());
        const stillInUse = allImages.some(
          img =>
            img.backgroundImageUrl === url ||
            (id && img.backgroundImageId === id)
        );

        if (stillInUse) {
          console.log('背景图片仍在使用，取消清理:', { url, id });
          return;
        }

        // 优先通过backgroundImageId查找，如果没有则通过URL查找
        const backgroundImage = id
          ? uploadedBackgroundImages.find(img => img.id === id)
          : uploadedBackgroundImages.find(img => img.url === url);

        if (backgroundImage) {
          console.log('删除背景图片:', backgroundImage.id);
          // 删除IndexedDB中的数据和更新状态
          await deleteBackgroundImage(backgroundImage.id);
        } else {
          console.log('未找到背景图片，可能已被删除:', { url, id });
          // 如果找不到，清理可能失效的blob URL
          if (url && url.startsWith('blob:')) {
            try {
              URL.revokeObjectURL(url);
              console.log('已清理失效的blob URL:', url);
            } catch (error) {
              console.error('清理失效blob URL失败:', error);
            }
          }
        }
      } catch (error) {
        console.error('清理自定义背景图片失败:', error);
      }
    };

    // 添加事件监听器
    window.addEventListener(
      'cleanupBackgroundImage',
      handleCleanupBackgroundImage as unknown as EventListener
    );

    return () => {
      // 清理事件监听器
      window.removeEventListener(
        'cleanupBackgroundImage',
        handleCleanupBackgroundImage as unknown as EventListener
      );
    };
  }, [uploadedBackgroundImages, deleteBackgroundImage]);

  // 编辑相关 hook
  const {
    images,
    currentImageId,
    currentImageObject,
    currentImageFromHistory,
    temporalState,
    currentScale,
    initialScale,
    canPan,
    isCompareActive,
    setIsCompareActive,
    forceCloseEraserTool,
    forceOpenEraserTool,
    isHandToolActive,
    canvasWrapperStyle,
    wrapperSize,
    canvasEditorRef,
    deleteDialogOpen,
    setDeleteDialogOpen,
    updateCurrentImageSettings,
    handleUndo,
    handleRedo,
    handleEraseOperationComplete,
    zoomIn,
    zoomOut,
    resetZoom,
    toggleHandTool,
    handleDownload,
    showDeleteConfirmation,
    confirmDeleteImage,
    cancelDeleteImage,
    handleSelectImage,
    triggerEraserToolOpen,
  } = useImageEditor(processingImageIds, handleImageSelectAfterDelete);

  // 自定义背景图片现在通过 useImageStorage hook 管理

  // 下载弹窗状态
  const [downloadPopoverOpen, setDownloadPopoverOpen] = useState(false);

  // 获取当前图片的各种状态
  const processedUrl = currentImageFromHistory?.processedUrl;
  const selectedBackgroundColor =
    currentImageFromHistory?.backgroundColor ?? 'transparent';
  const backgroundImageUrl = currentImageFromHistory?.backgroundImageUrl;
  const isBlurEnabled = currentImageFromHistory?.isBlurEnabled ?? false;
  const blurAmount = currentImageFromHistory?.blurAmount ?? 0;
  const isEraseMode = currentImageFromHistory?.isEraseMode ?? false;
  const isRestoreMode = currentImageFromHistory?.isRestoreMode ?? false;
  const eraseBrushSize = currentImageFromHistory?.eraseBrushSize ?? 80;

  // 背景去除错误状态
  const hasBackgroundRemovalError =
    currentImageFromHistory?.status === 'bg-remove-failed';

  // 处理图片选择，包括自动背景处理
  const handleImageSelect = async (imageId: string) => {
    const selectedImage = await handleSelectImage(imageId);

    // 使用通用的自动背景去除函数
    if (selectedImage) {
      await processImageBackgroundRemoval(selectedImage);
    }

    return selectedImage;
  };

  return (
    <div className='h-full flex flex-col bg-gray-50 overflow-hidden relative'>
      {/* 始终渲染 dropzone 的 input 元素，确保 open() 函数可用 */}
      <input {...getInputProps()} />

      <div className='h-full flex justify-center items-start mt-24'>
        <div className='relative flex justify-center items-center flex-col'>
          <div></div>
          {/* 未上传图片时的初始界面 */}
          {!currentImageObject ? (
            <ImageUploadArea
              getRootProps={getRootProps}
              open={open}
              isDragActive={isDragActive}
              handleLoadFromUrl={handleLoadFromUrl}
              handleLoadSampleImage={handleLoadSampleImage}
              imagesCount={images.length}
            />
          ) : (
            <>
              {/* 积分消耗显示 */}
              <div className='mb-4'>
                <CreditDisplay />
              </div>

              {/* Canvas 编辑器容器 */}
              <div className='relative' style={canvasWrapperStyle}>
                <CanvasImageEditor
                  ref={canvasEditorRef}
                  originalImgObject={currentImageObject}
                  processedUrl={processedUrl}
                  backgroundColor={selectedBackgroundColor}
                  backgroundImageUrl={backgroundImageUrl}
                  isCompareModeActive={isCompareActive}
                  scale={currentScale}
                  initialScale={initialScale}
                  onScaleChangeRequest={scale =>
                    console.log('缩放请求:', scale)
                  }
                  isBlurEnabled={isBlurEnabled}
                  blurAmount={blurAmount}
                  containerWidth={wrapperSize.width}
                  containerHeight={wrapperSize.height}
                  isEraseMode={isEraseMode}
                  isRestoreMode={isRestoreMode}
                  eraseBrushSize={eraseBrushSize}
                  onEraseOperationComplete={handleEraseOperationComplete}
                  isHandToolActive={isHandToolActive}
                />
                {/* API 加载遮罩 */}
                {isLoadingApi && currentImageObject && (
                  <div className='absolute inset-0 bg-black/70 bg-opacity-30 flex items-center justify-center z-20 rounded-lg pointer-events-auto'>
                    <StarryNightLoading starColor='yellow' starCount={50} />
                  </div>
                )}

                {/* 背景去除失败错误遮罩 */}
                <BackgroundRemovalErrorOverlay
                  isVisible={hasBackgroundRemovalError && !isLoadingApi}
                  onTryEraser={() => {
                    // 激活橡皮擦工具并打开工具弹窗
                    updateCurrentImageSettings({
                      isEraseMode: true,
                      isRestoreMode: false,
                      status: 'original', // 重置状态，清除错误
                    });
                    // 触发橡皮擦工具弹窗打开
                    triggerEraserToolOpen();
                  }}
                  onDeleteImage={() => {
                    if (currentImageId) {
                      showDeleteConfirmation(currentImageId);
                    }
                  }}
                />
              </div>
            </>
          )}

          {/* 底部工具栏 */}
          {currentImageObject && (
            <div className='w-full flex justify-between items-center gap-1.5 mt-4'>
              <div className='bg-white border border-[#e7e7e7] rounded-full h-10 px-4 flex items-center gap-2'>
                {/* 缩小按钮 */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant='ghost'
                      size='icon'
                      className='w-8 h-8 p-0 m-0'
                      onClick={zoomOut}
                      disabled={currentScale <= initialScale}
                    >
                      <Image
                        src='/apps/icons/reduce.svg'
                        alt='reduce'
                        width={24}
                        height={24}
                      />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Zoom out</TooltipContent>
                </Tooltip>

                {/* 缩放百分比 */}
                <span className='text-[12px]text-text-primary min-w-[40px] text-center m-0'>
                  {Math.round((currentScale / initialScale) * 100)}%
                </span>

                {/* 放大按钮 */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant='ghost'
                      size='icon'
                      className='w-8 h-8 p-0 m-0'
                      onClick={zoomIn}
                      disabled={currentScale >= initialScale * 3}
                    >
                      <Image
                        src='/apps/icons/amplify.svg'
                        alt='amplify'
                        width={24}
                        height={24}
                      />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Zoom in</TooltipContent>
                </Tooltip>

                {/* 分隔线 */}
                <div className='bg-[#e7e7e7] h-3 w-px' />

                {/* 手形工具 */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant='ghost'
                      size='icon'
                      className='w-8 h-8 p-0 m-0'
                      onClick={toggleHandTool}
                      disabled={!canPan}
                    >
                      <MoveIcon
                        className={`${isHandToolActive ? 'active' : ''} icon-interactive size-6`}
                      />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    {isHandToolActive
                      ? 'Deactivate hand tool'
                      : 'Hand tool - Click to drag'}
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant='ghost'
                      size='icon'
                      onClick={resetZoom}
                      disabled={currentScale === initialScale}
                      className='w-8 h-8 p-0 m-0'
                    >
                      <Image
                        src='/apps/icons/reset.svg'
                        alt='reset'
                        width={24}
                        height={24}
                      />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Reset</TooltipContent>
                </Tooltip>
              </div>
              <div className='bg-white border border-[#e7e7e7] rounded-full h-10 px-4 flex items-center gap-2'>
                {isLoadingApi ? (
                  // 加载中的骨架屏
                  <>
                    <div className='w-8 h-8 bg-gray-200 rounded animate-pulse'></div>
                    <div className='w-8 h-8 bg-gray-200 rounded animate-pulse'></div>
                    <div className='w-8 h-8 bg-gray-200 rounded animate-pulse'></div>
                  </>
                ) : (
                  // 工具栏按钮
                  <>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant='ghost'
                          size='icon'
                          onClick={handleUndo}
                          disabled={
                            !temporalState ||
                            temporalState.pastStates.length === 0
                          }
                          className='w-8 h-8 p-0 m-0'
                        >
                          <Image
                            src='/apps/icons/previous.svg'
                            alt='previous'
                            width={24}
                            height={24}
                          />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Previous step</TooltipContent>
                    </Tooltip>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant='ghost'
                          size='icon'
                          onClick={handleRedo}
                          disabled={
                            !temporalState ||
                            temporalState.futureStates.length === 0
                          }
                          className='w-8 h-8 p-0 m-0'
                        >
                          <Image
                            src='/apps/icons/next.svg'
                            alt='next'
                            width={24}
                            height={24}
                          />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Next step</TooltipContent>
                    </Tooltip>
                    {/* 分隔线 */}
                    <div className='bg-[#e7e7e7] h-3 w-px' />
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant='ghost'
                          size='icon'
                          onMouseDown={() => setIsCompareActive(true)}
                          onMouseUp={() => setIsCompareActive(false)}
                          onTouchStart={() => setIsCompareActive(true)}
                          onTouchEnd={() => setIsCompareActive(false)}
                          className='w-8 h-8 p-0 m-0'
                        >
                          <Image
                            src='/apps/icons/compare.svg'
                            alt='compare'
                            width={24}
                            height={24}
                          />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Compare original image</TooltipContent>
                    </Tooltip>
                  </>
                )}
              </div>
            </div>
          )}
        </div>

        {/* 右侧编辑面板 */}
        {currentImageObject && (
          <div
            className={`w-[340px] flex flex-col flex-shrink-0 ml-8 p-0 space-y-1 transition-opacity duration-300 ${isLoadingApi ? 'opacity-50 pointer-events-none' : 'opacity-100'}
            `}
          >
            {/* 功能选项列表 */}
            <div className='space-y-1'>
              {/* Change Background Colors */}
              <div>
                <BackgroundColorPicker
                  currentColor={selectedBackgroundColor}
                  onChangeColor={color => {
                    if (selectedBackgroundColor !== color) {
                      // 选择背景颜色时，清除背景图片（互斥逻辑）
                      updateCurrentImageSettings({
                        backgroundColor: color,
                        backgroundImageUrl: undefined,
                      });
                    }
                  }}
                >
                  <div className=' rounded-lg p-2 hover:bg-[#F0F0F0] cursor-pointer'>
                    <div className='flex items-center space-x-3'>
                      <div className='relative shrink-0 size-10'>
                        <div className='absolute border bg-white border-solid border-[#E7E7E7] inset-0 pointer-events-none rounded-lg' />
                        <div className='flex items-center justify-center relative size-full'>
                          <div className='size-6'>
                            <Image
                              src='/apps/icons/changeColors.svg'
                              alt='changeColors'
                              width={24}
                              height={24}
                            />
                          </div>
                        </div>
                      </div>
                      <div className='flex flex-col  justify-center leading-[1.5] not-italictext-text-primary text-base text-left'>
                        Change Background Colors
                      </div>
                    </div>
                  </div>
                </BackgroundColorPicker>
              </div>
              {/* Change Background Photos */}
              <div>
                <BackgroundImagePicker
                  currentBackgroundImageUrl={backgroundImageUrl}
                  onSelectImage={url => {
                    if (backgroundImageUrl !== url) {
                      // 查找对应的背景图片ID
                      let backgroundImageId: string | undefined;
                      if (url) {
                        if (url.startsWith('blob:')) {
                          // 自定义上传的背景图片
                          const matchedBackground =
                            uploadedBackgroundImages.find(bg => bg.url === url);
                          backgroundImageId = matchedBackground?.id;
                        } else if (url.startsWith('/apps/images/background/')) {
                          // 预设背景图片，生成固定ID
                          backgroundImageId = `preset-${url.split('/').pop()?.replace('.png', '')}`;
                        }
                      }

                      // 选择背景图片时，清除背景颜色（互斥逻辑）
                      updateCurrentImageSettings({
                        backgroundImageUrl: url,
                        backgroundImageId: backgroundImageId,
                        backgroundColor: url
                          ? 'transparent'
                          : selectedBackgroundColor,
                      });
                    }
                  }}
                  onFileUpload={async file => {
                    try {
                      const newImage = await addBackgroundImage(file);
                      // 上传新背景图片时，清除背景颜色（互斥逻辑）
                      updateCurrentImageSettings({
                        backgroundImageUrl: newImage.url,
                        backgroundImageId: newImage.id,
                        backgroundColor: 'transparent',
                      });
                    } catch (error) {
                      console.error('上传背景图片失败:', error);
                    }
                  }}
                  uploadedImages={uploadedBackgroundImages}
                >
                  <div className='rounded-lg p-2 hover:bg-[#F0F0F0] cursor-pointer'>
                    <div className='flex items-center space-x-3'>
                      <div className='relative shrink-0 size-10'>
                        <div className='absolute border bg-white border-solid border-[#E7E7E7] inset-0 pointer-events-none rounded-lg' />
                        <div className='flex items-center justify-center relative size-full'>
                          <div className='size-6'>
                            <Image
                              src='/apps/icons/changePhotos.svg'
                              alt='changePhotos'
                              width={24}
                              height={24}
                            />
                          </div>
                        </div>
                      </div>
                      <div className='flex flex-col  justify-center leading-[1.5] not-italictext-text-primary text-base text-left'>
                        Change Background Photos
                      </div>
                    </div>
                  </div>
                </BackgroundImagePicker>
              </div>
              {/* Erase / Restore */}
              <div>
                <EraserTool
                  isEraseMode={isEraseMode}
                  isRestoreMode={isRestoreMode}
                  eraseBrushSize={eraseBrushSize}
                  forceClose={forceCloseEraserTool}
                  forceOpen={forceOpenEraserTool}
                  onEraseModeChange={(isEraseMode, isRestoreMode) => {
                    updateCurrentImageSettings({
                      isEraseMode,
                      isRestoreMode,
                    });
                  }}
                  onBrushSizeChange={size => {
                    updateCurrentImageSettings({
                      eraseBrushSize: size,
                    });
                  }}
                >
                  <div className='rounded-lg p-2 hover:bg-[#F0F0F0] cursor-pointer'>
                    <div className='flex items-center space-x-3'>
                      <div className='relative shrink-0 size-10'>
                        <div className='absolute border bg-white border-solid border-[#E7E7E7] inset-0 pointer-events-none rounded-lg' />
                        <div className='flex items-center justify-center relative size-full'>
                          <div className='size-6'>
                            <Image
                              src='/apps/icons/erase.svg'
                              alt='erase'
                              width={24}
                              height={24}
                            />
                          </div>
                        </div>
                      </div>
                      <div className='flex flex-col  justify-center leading-[1.5] not-italictext-text-primary text-base text-left'>
                        Erase / Restore
                      </div>
                    </div>
                  </div>
                </EraserTool>
              </div>
              {/* Blur Background */}
              <div>
                <BackgroundBlurPicker
                  isBlurEnabled={isBlurEnabled}
                  blurAmount={blurAmount}
                  onBlurSettingsChange={settings => {
                    updateCurrentImageSettings({
                      isBlurEnabled: settings.isBlurEnabled,
                      blurAmount: settings.blurAmount,
                    });
                  }}
                >
                  <div className='rounded-lg p-2 hover:bg-[#F0F0F0] cursor-pointer'>
                    <div className='flex items-center space-x-3'>
                      <div className='relative shrink-0 size-10'>
                        <div className='absolute border bg-white border-solid border-[#E7E7E7] inset-0 pointer-events-none rounded-lg' />
                        <div className='flex items-center justify-center relative size-full'>
                          <div className='size-6'>
                            <Image
                              src='/apps/icons/blur.svg'
                              alt='blur'
                              width={24}
                              height={24}
                            />
                          </div>
                        </div>
                      </div>
                      <div className='flex flex-col  justify-center leading-[1.5] not-italictext-text-primary text-base text-left'>
                        Blur Background
                      </div>
                    </div>
                  </div>
                </BackgroundBlurPicker>
              </div>
            </div>

            {/* Download 按钮 */}
            <div className='mt-8'>
              <Popover
                open={downloadPopoverOpen}
                onOpenChange={setDownloadPopoverOpen}
              >
                <PopoverTrigger asChild>
                  <Button
                    className='bg-primary hover:bg-brand-primary-hover text-text-primary w-full h-12 rounded-lg text-base font-medium cursor-pointer'
                    disabled={
                      !currentImageObject ||
                      isLoadingApi ||
                      !currentImageFromHistory?.processedUrl
                    }
                  >
                    <div className='flex items-center space-x-2'>
                      <span className='text-text-primary text-base'>
                        Download
                      </span>
                      <Image
                        src='/apps/icons/down.svg'
                        alt='down'
                        width={24}
                        height={24}
                        className={`transition-transform duration-300 ease-in-out ${
                          downloadPopoverOpen ? 'rotate-180' : 'rotate-0'
                        }`}
                      />
                    </div>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className='w-[340px] p-0' align='start'>
                  <div className='bg-white border border-[#e7e7e7] rounded-xl shadow-[0px_10px_32px_0px_rgba(220,223,228,0.08),0px_8px_16px_0px_rgba(220,223,228,0.12),0px_7px_14px_0px_rgba(220,223,228,0.16)]'>
                    <div className='flex flex-col p-2 space-y-1'>
                      {/* Preview Option - Free */}
                      <div
                        className='rounded-lg p-2.5 hover:bg-[#FFF0B4] cursor-pointer'
                        onClick={() => handleDownload('free')}
                      >
                        <div className='flex items-center justify-between'>
                          <div className='flex items-center space-x-3'>
                            <div className='relative shrink-0 size-6'>
                              <Image
                                src='/apps/icons/preview.svg'
                                alt='preview'
                                width={24}
                                height={24}
                              />
                            </div>
                            <div className='flex flex-col text-text-primary'>
                              <span className='text-base font-medium'>
                                Preview
                              </span>
                              <span className='text-[14px] text-[#878787]'>
                                {formatImageDimensions(
                                  currentImageFromHistory,
                                  'free',
                                  wrapperSize
                                )}
                              </span>
                            </div>
                          </div>
                          <div className='bg-[#e7e7e7] rounded-full px-3 py-1'>
                            <span className='text-[14px] font-medium text-text-primary'>
                              Free
                            </span>
                          </div>
                        </div>
                      </div>
                      {/* Max Quality Option - Premium */}
                      <div
                        className='rounded-lg p-2.5 hover:bg-[#FFF0B4] cursor-pointer'
                        onClick={() => handleDownload('premium')}
                      >
                        <div className='flex items-center justify-between'>
                          <div className='flex items-center space-x-3'>
                            <div className='relative shrink-0 size-6'>
                              <Image
                                src='/apps/icons/max.svg'
                                alt='max'
                                width={24}
                                height={24}
                              />
                            </div>
                            <div className='flex flex-col text-text-primary'>
                              <span className='text-base font-medium'>
                                Max Quality
                              </span>
                              <span className='text-[14px] text-[#878787]'>
                                {formatImageDimensions(
                                  currentImageFromHistory,
                                  'premium',
                                  wrapperSize
                                )}
                              </span>
                            </div>
                          </div>
                          <div className='bg-[#FFCC03] rounded-full px-3 py-1'>
                            <span className='text-[14px] font-medium text-text-primary'>
                              Unlock
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>
        )}
      </div>

      {/* 底部留白和历史记录栏：仅当有图片时显示 */}
      {images.length > 0 && (
        <ImageHistoryBar
          images={images}
          selectedImageIds={
            new Set([currentImageId].filter(Boolean) as string[])
          }
          isLoadingApi={isLoadingApi}
          currentImageId={currentImageId}
          open={open}
          handleSelectImage={handleImageSelect}
          showDeleteConfirmation={showDeleteConfirmation}
          deleteDialogOpen={deleteDialogOpen}
          setDeleteDialogOpen={setDeleteDialogOpen}
          confirmDeleteImage={confirmDeleteImage}
          cancelDeleteImage={cancelDeleteImage}
          imagesCount={images.length}
        />
      )}
    </div>
  );
}
