'use client';

import { useAuthStore } from '@/store/accountStore';
import Image from 'next/image';
/**
 * 积分消耗显示组件
 * 显示当前用户的积分使用情况，UI参考Figma设计
 */
export function CreditDisplay() {
  const { userInfo } = useAuthStore();

  // 从用户信息中获取积分数据
  const currentScore = userInfo?.score ?? 0;
  const totalScore = userInfo?.total_score ? parseInt(userInfo.total_score) : 0;
  const usedCredits = totalScore - currentScore;

  return (
    <div className='bg-[rgba(255,204,3,0.1)] rounded-[100px] px-[10px] py-[10px] flex items-center justify-center gap-[10px] w-[293px] h-[40px]'>
      <div className='flex items-center gap-[8px]'>
        {/* AI 图标 */}
        <div>
          {/* AI 图标的渐变背景 */}
          <Image src='/apps/icons/brand.svg' alt='ai' width={24} height={24} />
        </div>

        {/* 文本内容 */}
        <div className='flex items-center gap-[8px]'>
          <span className='text-sm'>Advanced AI</span>

          {/* 分隔线 */}
          <div className='w-[1px] h-[12px]' />

          <span className='text-sm font-bold text-brand-orange'>
            {usedCredits}/{totalScore}
          </span>
          <span className='text-sm'>free credits used</span>
        </div>
      </div>
    </div>
  );
}
